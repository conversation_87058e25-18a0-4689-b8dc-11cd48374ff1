<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>TaskNest - Your Personal Todo Manager</title>
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <!-- Authentication Screen -->
  <div id="auth-container" class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="gradient-text">TaskNest</h1>
        <p class="auth-subtitle">Your Personal Todo Manager</p>
      </div>

      <!-- Login Form -->
      <form id="login-form" class="auth-form">
        <h2 class="form-title">Welcome Back</h2>
        <div class="input-group">
          <input
            type="email"
            id="login-email"
            placeholder="Email address"
            aria-label="Email address"
            required
          />
          <span class="input-icon">📧</span>
        </div>
        <div class="input-group">
          <input
            type="password"
            id="login-password"
            placeholder="Password"
            aria-label="Password"
            required
          />
          <span class="input-icon">🔒</span>
        </div>
        <button type="submit" class="auth-button primary">Sign In</button>
        <p class="auth-switch">
          Don't have an account?
          <button type="button" id="show-signup" class="link-button">Sign up</button>
        </p>
      </form>

      <!-- Signup Form -->
      <form id="signup-form" class="auth-form hidden">
        <h2 class="form-title">Create Account</h2>
        <div class="input-group">
          <input
            type="text"
            id="signup-name"
            placeholder="Full name"
            aria-label="Full name"
            required
          />
          <span class="input-icon">👤</span>
        </div>
        <div class="input-group">
          <input
            type="email"
            id="signup-email"
            placeholder="Email address"
            aria-label="Email address"
            required
          />
          <span class="input-icon">📧</span>
        </div>
        <div class="input-group">
          <input
            type="password"
            id="signup-password"
            placeholder="Password (min 6 characters)"
            aria-label="Password"
            required
            minlength="6"
          />
          <span class="input-icon">🔒</span>
        </div>
        <div class="input-group">
          <input
            type="password"
            id="signup-confirm-password"
            placeholder="Confirm password"
            aria-label="Confirm password"
            required
          />
          <span class="input-icon">🔒</span>
        </div>
        <button type="submit" class="auth-button primary">Create Account</button>
        <p class="auth-switch">
          Already have an account?
          <button type="button" id="show-login" class="link-button">Sign in</button>
        </p>
      </form>

      <div id="auth-error" class="error-message hidden"></div>
    </div>
  </div>

  <!-- Main Todo App (hidden initially) -->
  <main class="todo-app hidden" id="todo-app">
    <header class="app-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="gradient-text">TaskNest</h1>
          <p class="subtitle" id="user-greeting">Organize your day.</p>
        </div>
        <div class="header-right">
          <button id="logout-btn" class="logout-button" title="Logout">
            <span class="logout-icon">🚪</span>
          </button>
        </div>
      </div>
    </header>

    <section class="task-input-section">
      <form id="task-form">
        <input
          type="text"
          id="task-input"
          placeholder="What needs to be done?"
          aria-label="Task input"
          required
        />
        <button type="submit" class="gradient-button">Add Task</button>
      </form>
    </section>

    <section class="task-list-section">
      <div class="empty-state" id="empty-state">
        <img src="https://i.postimg.cc/sfcJnrHX/notepad.png" alt="Notepad with pencil icon" />
        <p class="no-tasks">No tasks yet</p>
        <p class="add-prompt">Add your first task above!</p>
      </div>

      <ul id="task-list" class="task-list" hidden></ul>
    </section>
  </main>

  <script src="script.js"></script>
</body>
</html>
