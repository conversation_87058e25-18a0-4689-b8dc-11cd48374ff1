/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  min-height: 100vh;
  color: #333;
  overflow-x: hidden;
}

/* Mobile status bar simulation */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: rgba(102, 126, 234, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Main container */
.todo-app {
  max-width: 400px;
  margin: 0 auto;
  padding: 60px 20px 20px;
  min-height: 100vh;
  position: relative;
}

/* Header styles */
.app-header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 20px;
}

.gradient-text {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* Task input section */
.task-input-section {
  margin-bottom: 40px;
}

#task-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

#task-input {
  width: 100%;
  padding: 18px 20px;
  border: none;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  font-size: 1rem;
  color: #333;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  outline: none;
}

#task-input::placeholder {
  color: #999;
  font-weight: 400;
}

#task-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

/* Gradient button */
.gradient-button {
  width: 100%;
  padding: 18px 20px;
  border: none;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.gradient-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gradient-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
}

.gradient-button:hover::before {
  left: 100%;
}

.gradient-button:active {
  transform: translateY(0);
}

/* Task list section */
.task-list-section {
  flex: 1;
}

/* Empty state */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.empty-state img {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  opacity: 0.8;
  filter: brightness(0) invert(1);
}

.no-tasks {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.add-prompt {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

/* Task list styles */
.task-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.task-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 16px 20px;
  margin-bottom: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.task-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.task-content {
  flex: 1;
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.task-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.complete-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.edit-btn {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
}

.delete-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
}

.task-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Completed task styles */
.task-item.completed .task-content {
  text-decoration: line-through;
  opacity: 0.6;
  color: #666;
}

.task-item.completed {
  background: rgba(255, 255, 255, 0.7);
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .todo-app {
    padding: 60px 16px 20px;
  }
  
  .gradient-text {
    font-size: 2.2rem;
  }
  
  #task-input, .gradient-button {
    padding: 16px 18px;
  }
  
  .task-item {
    padding: 14px 16px;
  }
  
  .task-btn {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

/* Animation for new tasks */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.task-item {
  animation: slideInUp 0.3s ease-out;
}

/* Loading state */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Focus styles for accessibility */
#task-input:focus,
.gradient-button:focus,
.task-btn:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Smooth transitions */
* {
  transition: all 0.3s ease;
}

/* Hidden state */
[hidden] {
  display: none !important;
}

/* Authentication Styles */
.auth-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  z-index: 2000;
}

.auth-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  animation: slideInUp 0.6s ease-out;
}

.auth-header {
  text-align: center;
  margin-bottom: 40px;
}

.auth-header .gradient-text {
  font-size: 2.8rem;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  color: #666;
  font-size: 1rem;
  font-weight: 400;
}

.auth-form {
  animation: fadeIn 0.4s ease-out;
}

.form-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 32px;
}

.input-group {
  position: relative;
  margin-bottom: 20px;
}

.input-group input {
  width: 100%;
  padding: 16px 20px 16px 50px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 1rem;
  color: #333;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
}

.input-group input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.input-group input:invalid:not(:placeholder-shown) {
  border-color: #f44336;
}

.input-group input:valid:not(:placeholder-shown) {
  border-color: #4CAF50;
}

.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
  pointer-events: none;
}

.auth-button {
  width: 100%;
  padding: 16px 20px;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.auth-button.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.auth-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
}

.auth-button.primary:active {
  transform: translateY(0);
}

.auth-switch {
  text-align: center;
  color: #666;
  font-size: 0.95rem;
}

.link-button {
  background: none;
  border: none;
  color: #667eea;
  font-weight: 600;
  cursor: pointer;
  text-decoration: underline;
  font-size: inherit;
}

.link-button:hover {
  color: #764ba2;
}

.error-message {
  background: #ffebee;
  color: #c62828;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  font-size: 0.9rem;
  border-left: 4px solid #f44336;
  animation: shake 0.5s ease-in-out;
}

.success-message {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 16px;
  font-size: 0.9rem;
  border-left: 4px solid #4CAF50;
}

/* Header updates for authenticated state */
.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logout-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.logout-icon {
  font-size: 1.2rem;
  filter: brightness(0) invert(1);
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #fff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Mobile responsiveness for auth */
@media (max-width: 480px) {
  .auth-card {
    padding: 30px 20px;
    margin: 10px;
  }

  .auth-header .gradient-text {
    font-size: 2.4rem;
  }

  .form-title {
    font-size: 1.6rem;
  }

  .input-group input {
    padding: 14px 18px 14px 45px;
  }

  .auth-button {
    padding: 14px 18px;
  }
}